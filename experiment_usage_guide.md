# 灯语识别实验自动化使用指南

## 🎯 实验概述

本实验系统实现了基于BeeSwarm平台的灯语编码识别自动化测试，支持：
- **距离区间随机采样**: 1-10m范围内9个区间，每区间随机100次测试
- **11种灯语模式**: 5种颜色×2种时序模式 + 常亮模式
- **智能位置分布**: 基于lab_106.world环境约束的智能车辆位置生成
- **实时数据收集**: MQTT通信同步真实距离和识别结果
- **自动化分析**: 准确率、混淆矩阵、距离误差等统计分析

## 🏗️ 系统架构

```
VSWARM11 (发布机器)          VSWARM15 (观测机器)
┌─────────────────────┐      ┌─────────────────────┐
│ 实验控制器           │      │ 识别结果监控器       │
│ - 位置控制          │      │ - 监听输出          │
│ - 灯语发布          │ MQTT │ - 解析结果          │
│ - 状态广播          │◄────►│ - 数据记录 📊       │
└─────────────────────┘      └─────────────────────┘
```

## 📋 环境要求

### 硬件要求
- VSWARM11: 发布机器，负责灯语发布和位置控制
- VSWARM15: 观测机器，负责视觉识别
- MQTT代理服务器: 192.168.11.58

### 软件要求
- ROS Noetic
- Python 3.x
- paho-mqtt
- 现有的BeeSwarm仿真环境

## 🚀 使用步骤

### 1. 环境准备

在VSWARM11上：
```bash
cd /home/<USER>/BeeSwarm/Code
# 确保实验控制器脚本可执行
chmod +x light_recognition_experiment_controller.py
```

在VSWARM15上：
```bash
cd /home/<USER>/BeeSwarm/Code
# 确保监控器脚本可执行
chmod +x recognition_result_monitor.py
```

### 2. 启动仿真环境

在两台机器上分别启动BeeSwarm仿真：
```bash
cd ~/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws
source devel/setup.bash
roslaunch vswarm_sim start_car_cpp.launch
```

### 3. 启动识别监控器 (VSWARM15)

```bash
cd /home/<USER>/BeeSwarm/Code
python3 recognition_result_monitor.py
```

监控器会：
- 自动启动`run_autonomous_swarm.sh`
- 监听输出并解析识别结果
- 通过MQTT发送结果给实验控制器

### 4. 启动实验控制器 (VSWARM11)

```bash
cd /home/<USER>/BeeSwarm/Code
python3 light_recognition_experiment_controller.py
```

实验控制器会：
- 显示实验配置信息
- 等待用户确认开始
- 自动执行所有试验组合

### 5. 实验执行

实验将自动执行：
- **9个距离区间** × **11种灯语模式** × **100次重复** = **9900次试验**
- 预计时间: 约22小时 (每次试验8秒)

每次试验流程：
1. 生成随机距离和位置
2. 设置两车位置
3. 发布灯语模式
4. 等待识别结果
5. 记录数据到CSV

## 📊 数据输出

### CSV数据文件
**保存位置**: VSWARM15 (观测机器)
文件名: `light_recognition_experiment_YYYYMMDD_HHMMSS.csv`

包含字段：
- `trial_id`: 试验编号
- `timestamp`: 时间戳
- `distance_range_min/max`: 距离区间
- `true_distance`: 真实距离
- `vswarm11_x/y, vswarm15_x/y`: 车辆位置
- `true_light_mode`: 真实灯语模式
- `estimated_distance`: 估算距离
- `recognized_light_mode`: 识别的灯语模式
- `recognition_success`: 识别是否成功
- `distance_error`: 距离估算误差
- `processing_time_ms`: 处理时间

### 实时统计
实验过程中会显示：
- 当前进度
- 识别成功率
- 距离估算误差
- 各区间统计

## 🔧 配置调整

### 修改实验参数

编辑 `light_recognition_experiment_controller.py`:

```python
self.experiment_config = {
    'distance_ranges': [
        (1.0, 2.0), (2.0, 3.0), ...  # 修改距离区间
    ],
    'light_modes': list(range(11)),   # 修改灯语模式
    'trials_per_combination': 100,    # 修改重复次数
    'recognition_wait_time': 5.0,     # 修改等待时间
}
```

### 修改环境约束

```python
self.environment_bounds = {
    'x_min': -3.5, 'x_max': 3.5,     # 修改X轴范围
    'y_min': -4.5, 'y_max': 4.5,     # 修改Y轴范围
    'safe_margin': 0.5                # 修改安全边距
}
```

### 修改输出解析

编辑 `recognition_result_monitor.py` 中的正则表达式：

```python
self.patterns = {
    'distance': re.compile(r'距离=(\d+\.?\d*)m'),
    'light_pattern': re.compile(r'检测到灯语:.*→\s*指令(\d+)'),
    # 根据实际输出格式调整
}
```

## 🛠️ 故障排除

### 常见问题

1. **MQTT连接失败**
   - 检查网络连接
   - 确认MQTT代理服务器地址
   - 检查用户名密码

2. **位置设置失败**
   - 确认Gazebo仿真正在运行
   - 检查车辆模型是否存在
   - 确认位置在环境边界内

3. **识别结果超时**
   - 检查VSWARM15上的监控器是否运行
   - 确认`run_autonomous_swarm.sh`正常执行
   - 调整等待时间参数

4. **数据记录异常**
   - 检查文件写入权限
   - 确认磁盘空间充足
   - 检查CSV文件格式

### 调试模式

启用详细日志：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📈 后续分析

实验完成后，可以使用生成的CSV数据进行：
- 准确率随距离变化分析
- 灯语混淆矩阵生成
- 距离估算精度分析
- 处理时间统计
- 仿真与实物对比

建议使用Python的pandas、matplotlib、seaborn等库进行数据分析和可视化。
