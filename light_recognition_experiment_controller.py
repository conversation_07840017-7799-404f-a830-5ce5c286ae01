#!/usr/bin/env python3
"""
灯语识别实验自动化控制器
基于lab_106.world环境，智能分布VSWARM11和VSWARM15位置
实现区间随机距离采样的灯语识别实验
"""

import rospy
import random
import math
import time
import json
import csv
import subprocess
import threading
from datetime import datetime
from std_msgs.msg import Int32
import paho.mqtt.client as mqtt

class LightRecognitionExperimentController:
    def __init__(self):
        rospy.init_node('light_recognition_experiment_controller', anonymous=True)
        
        # 实验配置
        self.experiment_config = {
            'distance_ranges': [
                (1.0, 2.0), (2.0, 3.0), (3.0, 4.0), (4.0, 5.0), (5.0, 6.0),
                (6.0, 7.0), (7.0, 8.0), (8.0, 9.0), (9.0, 10.0)
            ],
            'light_modes': list(range(11)),  # 0-10 对应11种灯语模式
            'trials_per_combination': 100,
            'recognition_wait_time': 5.0,  # 等待识别完成的时间(秒)
        }
        
        # Lab_106环境约束
        self.environment_bounds = {
            'x_min': -3.5, 'x_max': 3.5,
            'y_min': -4.5, 'y_max': 4.5,
            'safe_margin': 0.5  # 距离墙壁的安全边距
        }
        
        # LED控制发布者
        self.led_pub = rospy.Publisher('/VSWARM11/led_mode', Int32, queue_size=10)
        
        # MQTT配置 (基于现有BeeSwarm配置)
        self.mqtt_config = {
            'broker': '192.168.11.58',
            'port': 1883,
            'username': 'b7986fb66280bcee',
            'password': 'Mx9B2EVhH79BY783Zp6rPcrFHv70kQIqJ2i9B4EbdevZ3M',
            'keepalive': 60
        }
        
        # 实验数据记录
        self.experiment_data = []
        self.current_trial = 0
        self.total_trials = 0

        # 识别结果接收
        self.recognition_result_queue = []
        self.waiting_for_result = False
        
        # 计算总试验次数
        self.total_trials = (len(self.experiment_config['distance_ranges']) * 
                           len(self.experiment_config['light_modes']) * 
                           self.experiment_config['trials_per_combination'])
        
        print(f"🎯 实验总计: {self.total_trials} 次试验")
        
        # 初始化MQTT
        self.setup_mqtt()
        
        # 数据文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.data_file = f"light_recognition_experiment_{timestamp}.csv"
        self.init_data_file()
    
    def setup_mqtt(self):
        """设置MQTT连接"""
        self.mqtt_client = mqtt.Client("experiment_controller")
        self.mqtt_client.username_pw_set(
            self.mqtt_config['username'], 
            self.mqtt_config['password']
        )
        
        # 设置回调函数
        self.mqtt_client.on_connect = self.on_mqtt_connect
        self.mqtt_client.on_message = self.on_mqtt_message

        try:
            self.mqtt_client.connect(
                self.mqtt_config['broker'],
                self.mqtt_config['port'],
                self.mqtt_config['keepalive']
            )
            self.mqtt_client.loop_start()
            print("✅ MQTT连接成功")
        except Exception as e:
            print(f"❌ MQTT连接失败: {e}")

    def on_mqtt_connect(self, client, userdata, flags, rc):
        """MQTT连接回调"""
        if rc == 0:
            # 订阅识别结果话题
            client.subscribe("/experiment/recognition_result")
            print("📡 已订阅识别结果话题")
        else:
            print(f"❌ MQTT连接失败，返回码: {rc}")

    def on_mqtt_message(self, client, userdata, msg):
        """MQTT消息回调"""
        try:
            topic = msg.topic
            payload = json.loads(msg.payload.decode())

            if topic == "/experiment/recognition_result":
                # 接收到识别结果
                self.recognition_result_queue.append(payload)
                print(f"📥 接收到识别结果: 试验{payload.get('trial_id')}")

        except Exception as e:
            print(f"❌ 处理MQTT消息失败: {e}")
    
    def init_data_file(self):
        """初始化CSV数据文件"""
        headers = [
            'trial_id', 'timestamp', 'distance_range_min', 'distance_range_max',
            'true_distance', 'vswarm11_x', 'vswarm11_y', 'vswarm15_x', 'vswarm15_y',
            'true_light_mode', 'estimated_distance', 'recognized_light_mode',
            'recognition_success', 'distance_error', 'processing_time_ms'
        ]
        
        with open(self.data_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(headers)
        
        print(f"📊 数据文件已创建: {self.data_file}")
    
    def generate_smart_positions(self, target_distance):
        """
        智能生成两车位置，确保在环境边界内且距离符合要求
        
        策略:
        1. 随机选择VSWARM15位置(观测车)
        2. 基于目标距离和环境约束计算VSWARM11位置(发布车)
        3. 确保两车都在安全区域内
        """
        max_attempts = 100
        
        for attempt in range(max_attempts):
            # 随机选择VSWARM15位置 (观测车)
            vswarm15_x = random.uniform(
                self.environment_bounds['x_min'] + self.environment_bounds['safe_margin'],
                self.environment_bounds['x_max'] - self.environment_bounds['safe_margin']
            )
            vswarm15_y = random.uniform(
                self.environment_bounds['y_min'] + self.environment_bounds['safe_margin'],
                self.environment_bounds['y_max'] - self.environment_bounds['safe_margin']
            )
            
            # 随机选择方向角度
            angle = random.uniform(0, 2 * math.pi)
            
            # 计算VSWARM11位置 (发布车)
            vswarm11_x = vswarm15_x + target_distance * math.cos(angle)
            vswarm11_y = vswarm15_y + target_distance * math.sin(angle)
            
            # 检查VSWARM11是否在边界内
            if (self.environment_bounds['x_min'] + self.environment_bounds['safe_margin'] <= vswarm11_x <= 
                self.environment_bounds['x_max'] - self.environment_bounds['safe_margin'] and
                self.environment_bounds['y_min'] + self.environment_bounds['safe_margin'] <= vswarm11_y <= 
                self.environment_bounds['y_max'] - self.environment_bounds['safe_margin']):
                
                # 验证实际距离
                actual_distance = math.sqrt((vswarm11_x - vswarm15_x)**2 + (vswarm11_y - vswarm15_y)**2)
                
                return {
                    'vswarm11': (vswarm11_x, vswarm11_y),
                    'vswarm15': (vswarm15_x, vswarm15_y),
                    'actual_distance': actual_distance
                }
        
        raise Exception(f"无法在{max_attempts}次尝试内找到合适的位置组合，目标距离: {target_distance}m")
    
    def set_vehicle_position(self, vehicle_name, x, y):
        """设置车辆位置"""
        try:
            # 使用现有的C++位置设置程序
            cmd = f"cd /home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws && echo '{x}\\n{y}' | ./devel/lib/cpp_version/Set_Car_Model_State_Simple"
            
            # 在对应的车辆上执行 (这里需要根据实际情况调整)
            if vehicle_name == "VSWARM11":
                # 在VSWARM11上执行位置设置
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
            elif vehicle_name == "VSWARM15":
                # 在VSWARM15上执行位置设置  
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print(f"✅ {vehicle_name} 位置设置成功: ({x:.2f}, {y:.2f})")
                return True
            else:
                print(f"❌ {vehicle_name} 位置设置失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 设置{vehicle_name}位置时出错: {e}")
            return False
    
    def publish_light_mode(self, mode):
        """发布灯语模式"""
        try:
            msg = Int32()
            msg.data = mode
            self.led_pub.publish(msg)
            
            # 同时通过MQTT广播真实灯语信息
            mqtt_data = {
                'type': 'light_command',
                'timestamp': time.time(),
                'sender': 'VSWARM11',
                'light_mode': mode,
                'trial_id': self.current_trial
            }
            
            self.mqtt_client.publish('/experiment/light_command', json.dumps(mqtt_data))
            print(f"🔆 发布灯语模式: {mode}")
            return True
            
        except Exception as e:
            print(f"❌ 发布灯语模式失败: {e}")
            return False
    
    def broadcast_experiment_state(self, state_data):
        """通过MQTT广播实验状态"""
        try:
            mqtt_data = {
                'type': 'experiment_state',
                'timestamp': time.time(),
                **state_data
            }
            
            self.mqtt_client.publish('/experiment/state', json.dumps(mqtt_data))
            
        except Exception as e:
            print(f"❌ 广播实验状态失败: {e}")
    
    def wait_for_recognition_result(self, timeout=10):
        """等待识别结果，从MQTT接收真实的识别结果"""
        print(f"⏳ 等待识别结果... ({timeout}秒)")

        self.waiting_for_result = True
        start_time = time.time()

        # 清空之前的结果队列
        self.recognition_result_queue.clear()

        # 等待识别结果
        while time.time() - start_time < timeout:
            if self.recognition_result_queue:
                # 获取最新的识别结果
                result = self.recognition_result_queue[-1]

                # 检查是否是当前试验的结果
                if result.get('trial_id') == self.current_trial:
                    self.waiting_for_result = False
                    print(f"✅ 收到识别结果: 试验{result.get('trial_id')}")
                    return result

            time.sleep(0.1)  # 短暂等待

        # 超时处理
        self.waiting_for_result = False
        print(f"⏰ 等待识别结果超时 ({timeout}秒)")

        # 返回默认结果
        return {
            'estimated_distance': -1,
            'recognized_light_mode': -1,
            'processing_time_ms': -1,
            'confidence': 0.0,
            'timeout': True
        }

    def run_single_trial(self, distance_range, light_mode, trial_num):
        """执行单次试验"""
        self.current_trial += 1

        print(f"\n🔬 试验 {self.current_trial}/{self.total_trials}")
        print(f"   距离区间: {distance_range[0]}-{distance_range[1]}m")
        print(f"   灯语模式: {light_mode}")
        print(f"   区间内第: {trial_num}/100 次")

        # 1. 生成随机距离
        target_distance = random.uniform(distance_range[0], distance_range[1])

        # 2. 生成智能位置分布
        try:
            positions = self.generate_smart_positions(target_distance)
            vswarm11_pos = positions['vswarm11']
            vswarm15_pos = positions['vswarm15']
            actual_distance = positions['actual_distance']

            print(f"   🎯 目标距离: {target_distance:.2f}m, 实际距离: {actual_distance:.2f}m")
            print(f"   📍 VSWARM11: ({vswarm11_pos[0]:.2f}, {vswarm11_pos[1]:.2f})")
            print(f"   📍 VSWARM15: ({vswarm15_pos[0]:.2f}, {vswarm15_pos[1]:.2f})")

        except Exception as e:
            print(f"❌ 位置生成失败: {e}")
            return False

        # 3. 设置车辆位置
        if not self.set_vehicle_position("VSWARM11", vswarm11_pos[0], vswarm11_pos[1]):
            print("❌ VSWARM11位置设置失败")
            return False

        if not self.set_vehicle_position("VSWARM15", vswarm15_pos[0], vswarm15_pos[1]):
            print("❌ VSWARM15位置设置失败")
            return False

        # 4. 等待位置稳定
        time.sleep(2.0)

        # 5. 广播实验状态
        experiment_state = {
            'trial_id': self.current_trial,
            'distance_range': distance_range,
            'true_distance': actual_distance,
            'true_light_mode': light_mode,
            'vswarm11_position': vswarm11_pos,
            'vswarm15_position': vswarm15_pos
        }
        self.broadcast_experiment_state(experiment_state)

        # 6. 发布灯语模式
        if not self.publish_light_mode(light_mode):
            print("❌ 灯语模式发布失败")
            return False

        # 7. 等待识别结果
        start_time = time.time()
        recognition_result = self.wait_for_recognition_result(
            timeout=self.experiment_config['recognition_wait_time']
        )
        end_time = time.time()

        # 8. 记录数据
        trial_data = {
            'trial_id': self.current_trial,
            'timestamp': datetime.now().isoformat(),
            'distance_range_min': distance_range[0],
            'distance_range_max': distance_range[1],
            'true_distance': actual_distance,
            'vswarm11_x': vswarm11_pos[0],
            'vswarm11_y': vswarm11_pos[1],
            'vswarm15_x': vswarm15_pos[0],
            'vswarm15_y': vswarm15_pos[1],
            'true_light_mode': light_mode,
            'estimated_distance': recognition_result.get('estimated_distance', -1),
            'recognized_light_mode': recognition_result.get('recognized_light_mode', -1),
            'recognition_success': (recognition_result.get('recognized_light_mode', -1) == light_mode),
            'distance_error': abs(recognition_result.get('estimated_distance', 0) - actual_distance),
            'processing_time_ms': recognition_result.get('processing_time_ms', -1)
        }

        # 保存到CSV
        self.save_trial_data(trial_data)

        # 显示结果
        success = "✅" if trial_data['recognition_success'] else "❌"
        print(f"   {success} 识别结果: 模式{recognition_result.get('recognized_light_mode', -1)} "
              f"(真实: {light_mode})")
        print(f"   📏 距离误差: {trial_data['distance_error']:.2f}m")

        return True

    def save_trial_data(self, trial_data):
        """保存试验数据到CSV文件"""
        try:
            with open(self.data_file, 'a', newline='') as f:
                writer = csv.writer(f)
                writer.writerow([
                    trial_data['trial_id'], trial_data['timestamp'],
                    trial_data['distance_range_min'], trial_data['distance_range_max'],
                    trial_data['true_distance'], trial_data['vswarm11_x'], trial_data['vswarm11_y'],
                    trial_data['vswarm15_x'], trial_data['vswarm15_y'], trial_data['true_light_mode'],
                    trial_data['estimated_distance'], trial_data['recognized_light_mode'],
                    trial_data['recognition_success'], trial_data['distance_error'],
                    trial_data['processing_time_ms']
                ])

            self.experiment_data.append(trial_data)

        except Exception as e:
            print(f"❌ 保存数据失败: {e}")

    def run_experiment(self):
        """运行完整实验"""
        print("🚀 开始灯语识别实验")
        print(f"📊 实验配置:")
        print(f"   距离区间: {len(self.experiment_config['distance_ranges'])} 个")
        print(f"   灯语模式: {len(self.experiment_config['light_modes'])} 种")
        print(f"   每组重复: {self.experiment_config['trials_per_combination']} 次")
        print(f"   总试验数: {self.total_trials} 次")
        print(f"   预计时间: {self.total_trials * 8 / 3600:.1f} 小时")

        # 确认开始
        input("\n按Enter键开始实验...")

        start_time = time.time()
        successful_trials = 0

        try:
            # 遍历所有距离区间
            for dist_range in self.experiment_config['distance_ranges']:
                print(f"\n🎯 开始距离区间: {dist_range[0]}-{dist_range[1]}m")

                # 遍历所有灯语模式
                for light_mode in self.experiment_config['light_modes']:
                    print(f"\n💡 灯语模式: {light_mode}")

                    # 每个组合重复指定次数
                    for trial_num in range(1, self.experiment_config['trials_per_combination'] + 1):
                        if self.run_single_trial(dist_range, light_mode, trial_num):
                            successful_trials += 1

                        # 试验间隔
                        time.sleep(1.0)

                        # 检查是否需要暂停
                        if rospy.is_shutdown():
                            print("\n⏹️ 收到停止信号，实验中断")
                            break

                    if rospy.is_shutdown():
                        break

                if rospy.is_shutdown():
                    break

        except KeyboardInterrupt:
            print("\n⏹️ 用户中断实验")

        except Exception as e:
            print(f"\n❌ 实验过程中出错: {e}")

        finally:
            # 实验总结
            end_time = time.time()
            duration = end_time - start_time

            print(f"\n📊 实验完成!")
            print(f"   成功试验: {successful_trials}/{self.current_trial}")
            print(f"   实验时长: {duration/3600:.2f} 小时")
            print(f"   数据文件: {self.data_file}")

            # 生成初步统计
            self.generate_summary_report()

    def generate_summary_report(self):
        """生成实验总结报告"""
        if not self.experiment_data:
            print("❌ 没有实验数据可分析")
            return

        print("\n📈 实验总结:")

        # 总体准确率
        total_trials = len(self.experiment_data)
        successful_recognitions = sum(1 for data in self.experiment_data if data['recognition_success'])
        overall_accuracy = successful_recognitions / total_trials * 100

        print(f"   总体识别准确率: {overall_accuracy:.1f}% ({successful_recognitions}/{total_trials})")

        # 按距离区间统计
        distance_stats = {}
        for data in self.experiment_data:
            range_key = f"{data['distance_range_min']}-{data['distance_range_max']}"
            if range_key not in distance_stats:
                distance_stats[range_key] = {'total': 0, 'success': 0}

            distance_stats[range_key]['total'] += 1
            if data['recognition_success']:
                distance_stats[range_key]['success'] += 1

        print("\n   各距离区间准确率:")
        for range_key, stats in distance_stats.items():
            accuracy = stats['success'] / stats['total'] * 100
            print(f"     {range_key}m: {accuracy:.1f}% ({stats['success']}/{stats['total']})")

        # 距离估算误差统计
        distance_errors = [data['distance_error'] for data in self.experiment_data if data['distance_error'] >= 0]
        if distance_errors:
            avg_error = sum(distance_errors) / len(distance_errors)
            print(f"\n   平均距离估算误差: {avg_error:.2f}m")


def main():
    """主函数"""
    try:
        controller = LightRecognitionExperimentController()
        controller.run_experiment()

    except rospy.ROSInterruptException:
        print("ROS中断")
    except Exception as e:
        print(f"程序错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
