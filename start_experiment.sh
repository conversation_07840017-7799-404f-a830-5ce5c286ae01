#!/bin/bash
# 灯语识别实验启动脚本

echo "🎯 BeeSwarm灯语识别实验自动化系统"
echo "=================================="

# 检查当前主机
HOSTNAME=$(hostname)
echo "📍 当前主机: $HOSTNAME"

# 检查Python依赖
echo "🔍 检查Python依赖..."
python3 -c "import paho.mqtt.client as mqtt; print('✅ paho-mqtt 可用')" 2>/dev/null || {
    echo "❌ paho-mqtt 未安装，正在安装..."
    pip3 install paho-mqtt
}

python3 -c "import rospy; print('✅ rospy 可用')" 2>/dev/null || {
    echo "❌ ROS环境未配置"
    exit 1
}

# 检查文件权限
chmod +x light_recognition_experiment_controller.py
chmod +x recognition_result_monitor.py

echo ""
echo "🚀 请选择启动模式:"
echo "1. VSWARM11 - 实验控制器 (发布机器)"
echo "2. VSWARM15 - 识别监控器 (观测机器)"
echo "3. 查看使用指南"
echo "4. 退出"

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo ""
        echo "🎮 启动实验控制器 (VSWARM11)"
        echo "================================"
        echo "⚠️  请确保:"
        echo "   1. VSWARM15上的识别监控器已启动"
        echo "   2. 两台机器的仿真环境都在运行"
        echo "   3. MQTT代理服务器可访问"
        echo ""
        read -p "确认继续? (y/N): " confirm
        
        if [[ $confirm =~ ^[Yy]$ ]]; then
            echo "🚀 启动实验控制器..."
            python3 light_recognition_experiment_controller.py
        else
            echo "❌ 用户取消"
        fi
        ;;
        
    2)
        echo ""
        echo "👁️  启动识别监控器 (VSWARM15)"
        echo "================================"
        echo "⚠️  请确保:"
        echo "   1. run_autonomous_swarm.sh 可以正常运行"
        echo "   2. 仿真环境已启动"
        echo "   3. MQTT代理服务器可访问"
        echo ""
        read -p "确认继续? (y/N): " confirm
        
        if [[ $confirm =~ ^[Yy]$ ]]; then
            echo "🚀 启动识别监控器..."
            python3 recognition_result_monitor.py
        else
            echo "❌ 用户取消"
        fi
        ;;
        
    3)
        echo ""
        echo "📖 显示使用指南"
        echo "================================"
        if [ -f "experiment_usage_guide.md" ]; then
            cat experiment_usage_guide.md
        else
            echo "❌ 使用指南文件不存在"
        fi
        ;;
        
    4)
        echo "👋 退出"
        exit 0
        ;;
        
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac
