#!/usr/bin/env python3
"""
灯语识别实验结果分析器
分析VSWARM15上保存的实验数据，生成统计报告和图表
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os
import glob

class ExperimentResultAnalyzer:
    def __init__(self, data_file=None):
        """
        初始化分析器
        
        参数:
            data_file: CSV数据文件路径，如果为None则自动查找最新文件
        """
        if data_file is None:
            # 自动查找最新的实验数据文件
            pattern = "light_recognition_experiment_*.csv"
            files = glob.glob(pattern)
            if not files:
                raise FileNotFoundError("未找到实验数据文件")
            data_file = max(files, key=os.path.getctime)
            print(f"📁 自动选择数据文件: {data_file}")
        
        self.data_file = data_file
        self.df = None
        self.load_data()
    
    def load_data(self):
        """加载实验数据"""
        try:
            self.df = pd.read_csv(self.data_file)
            print(f"📊 成功加载数据: {len(self.df)} 条记录")
            
            # 数据预处理
            self.df['distance_range'] = self.df['distance_range_min'].astype(str) + '-' + self.df['distance_range_max'].astype(str) + 'm'
            self.df['timestamp'] = pd.to_datetime(self.df['timestamp'])
            
        except Exception as e:
            raise Exception(f"加载数据失败: {e}")
    
    def generate_summary_report(self):
        """生成总结报告"""
        print("\n" + "="*60)
        print("🎯 灯语识别实验结果分析报告")
        print("="*60)
        
        # 基本统计
        total_trials = len(self.df)
        successful_trials = self.df['recognition_success'].sum()
        overall_accuracy = successful_trials / total_trials * 100
        
        print(f"\n📈 总体统计:")
        print(f"   总试验次数: {total_trials}")
        print(f"   成功识别: {successful_trials}")
        print(f"   总体准确率: {overall_accuracy:.2f}%")
        
        # 按距离区间统计
        print(f"\n📏 各距离区间准确率:")
        distance_stats = self.df.groupby('distance_range').agg({
            'recognition_success': ['count', 'sum', 'mean']
        }).round(3)
        
        for distance_range in distance_stats.index:
            count = distance_stats.loc[distance_range, ('recognition_success', 'count')]
            success = distance_stats.loc[distance_range, ('recognition_success', 'sum')]
            accuracy = distance_stats.loc[distance_range, ('recognition_success', 'mean')] * 100
            print(f"   {distance_range}: {accuracy:.1f}% ({success}/{count})")
        
        # 按灯语模式统计
        print(f"\n💡 各灯语模式准确率:")
        light_stats = self.df.groupby('true_light_mode').agg({
            'recognition_success': ['count', 'sum', 'mean']
        }).round(3)
        
        for light_mode in sorted(light_stats.index):
            count = light_stats.loc[light_mode, ('recognition_success', 'count')]
            success = light_stats.loc[light_mode, ('recognition_success', 'sum')]
            accuracy = light_stats.loc[light_mode, ('recognition_success', 'mean')] * 100
            print(f"   模式{light_mode}: {accuracy:.1f}% ({success}/{count})")
        
        # 距离估算精度
        valid_distance = self.df[self.df['estimated_distance'] > 0]
        if len(valid_distance) > 0:
            avg_distance_error = valid_distance['distance_error'].mean()
            std_distance_error = valid_distance['distance_error'].std()
            print(f"\n📐 距离估算精度:")
            print(f"   平均误差: {avg_distance_error:.2f}m")
            print(f"   标准差: {std_distance_error:.2f}m")
            print(f"   有效估算: {len(valid_distance)}/{total_trials}")
        
        # 处理时间统计
        valid_time = self.df[self.df['processing_time_ms'] > 0]
        if len(valid_time) > 0:
            avg_time = valid_time['processing_time_ms'].mean()
            std_time = valid_time['processing_time_ms'].std()
            print(f"\n⏱️ 处理时间统计:")
            print(f"   平均时间: {avg_time:.0f}ms")
            print(f"   标准差: {std_time:.0f}ms")
    
    def plot_accuracy_by_distance(self, save_path=None):
        """绘制准确率随距离变化图"""
        plt.figure(figsize=(12, 6))
        
        # 计算各距离区间的准确率和置信区间
        distance_stats = self.df.groupby('distance_range_min').agg({
            'recognition_success': ['count', 'sum', 'mean', 'std']
        })
        
        distances = distance_stats.index
        accuracies = distance_stats[('recognition_success', 'mean')] * 100
        counts = distance_stats[('recognition_success', 'count')]
        
        # 计算95%置信区间
        confidence_intervals = 1.96 * np.sqrt(accuracies * (100 - accuracies) / counts)
        
        plt.errorbar(distances, accuracies, yerr=confidence_intervals, 
                    marker='o', linewidth=2, markersize=8, capsize=5)
        
        plt.xlabel('距离 (m)', fontsize=12)
        plt.ylabel('识别准确率 (%)', fontsize=12)
        plt.title('灯语识别准确率随距离变化', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.ylim(0, 105)
        
        # 添加数据点标签
        for x, y in zip(distances, accuracies):
            plt.annotate(f'{y:.1f}%', (x, y), textcoords="offset points", 
                        xytext=(0,10), ha='center', fontsize=10)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 准确率图表已保存: {save_path}")
        
        plt.show()
    
    def plot_confusion_matrix(self, save_path=None):
        """绘制灯语识别混淆矩阵"""
        from sklearn.metrics import confusion_matrix
        
        # 只考虑有效识别结果
        valid_data = self.df[self.df['recognized_light_mode'] >= 0]
        
        if len(valid_data) == 0:
            print("❌ 没有有效的识别结果数据")
            return
        
        # 计算混淆矩阵
        cm = confusion_matrix(valid_data['true_light_mode'], 
                             valid_data['recognized_light_mode'])
        
        # 绘制热力图
        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=range(11), yticklabels=range(11))
        
        plt.xlabel('识别结果', fontsize=12)
        plt.ylabel('真实灯语', fontsize=12)
        plt.title('灯语识别混淆矩阵', fontsize=14, fontweight='bold')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 混淆矩阵已保存: {save_path}")
        
        plt.show()
    
    def plot_distance_error_distribution(self, save_path=None):
        """绘制距离估算误差分布"""
        valid_data = self.df[(self.df['distance_error'] >= 0) & (self.df['distance_error'] < 20)]
        
        if len(valid_data) == 0:
            print("❌ 没有有效的距离误差数据")
            return
        
        plt.figure(figsize=(12, 8))
        
        # 子图1: 误差分布直方图
        plt.subplot(2, 2, 1)
        plt.hist(valid_data['distance_error'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        plt.xlabel('距离误差 (m)')
        plt.ylabel('频次')
        plt.title('距离估算误差分布')
        plt.grid(True, alpha=0.3)
        
        # 子图2: 按距离区间的误差箱线图
        plt.subplot(2, 2, 2)
        valid_data.boxplot(column='distance_error', by='distance_range', ax=plt.gca())
        plt.xlabel('距离区间')
        plt.ylabel('距离误差 (m)')
        plt.title('各距离区间误差分布')
        plt.xticks(rotation=45)
        
        # 子图3: 真实距离 vs 估算距离散点图
        plt.subplot(2, 2, 3)
        plt.scatter(valid_data['true_distance'], valid_data['estimated_distance'], 
                   alpha=0.6, s=20)
        
        # 添加理想线 (y=x)
        min_dist = min(valid_data['true_distance'].min(), valid_data['estimated_distance'].min())
        max_dist = max(valid_data['true_distance'].max(), valid_data['estimated_distance'].max())
        plt.plot([min_dist, max_dist], [min_dist, max_dist], 'r--', alpha=0.8, label='理想线')
        
        plt.xlabel('真实距离 (m)')
        plt.ylabel('估算距离 (m)')
        plt.title('真实距离 vs 估算距离')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 子图4: 误差随真实距离变化
        plt.subplot(2, 2, 4)
        plt.scatter(valid_data['true_distance'], valid_data['distance_error'], 
                   alpha=0.6, s=20, color='orange')
        plt.xlabel('真实距离 (m)')
        plt.ylabel('距离误差 (m)')
        plt.title('误差随距离变化')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 距离误差分析图已保存: {save_path}")
        
        plt.show()
    
    def generate_full_report(self, output_dir="analysis_results"):
        """生成完整分析报告"""
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        print("🚀 开始生成完整分析报告...")
        
        # 生成文字报告
        self.generate_summary_report()
        
        # 生成图表
        self.plot_accuracy_by_distance(f"{output_dir}/accuracy_by_distance_{timestamp}.png")
        self.plot_confusion_matrix(f"{output_dir}/confusion_matrix_{timestamp}.png")
        self.plot_distance_error_distribution(f"{output_dir}/distance_error_analysis_{timestamp}.png")
        
        print(f"\n✅ 分析报告生成完成，保存在: {output_dir}/")


def main():
    """主函数"""
    try:
        print("🔍 灯语识别实验结果分析器")
        print("="*40)
        
        # 创建分析器
        analyzer = ExperimentResultAnalyzer()
        
        # 生成完整报告
        analyzer.generate_full_report()
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
