#include <iostream>
#include <string>
#include <unistd.h>
#include <ros/ros.h>
#include <gazebo_msgs/SetModelState.h>
#include <gazebo_msgs/ModelState.h>

int main(int argc, char** argv) {
    // 初始化ROS节点
    ros::init(argc, argv, "set_car_position_cpp");
    ros::NodeHandle nh;
    
    // 获取主机名作为模型名
    char hostname[256];
    gethostname(hostname, sizeof(hostname));
    std::string model_name(hostname);
    
    // 等待服务可用
    std::cout << "等待Gazebo set_model_state服务..." << std::endl;
    ros::service::waitForService("/gazebo/set_model_state");
    
    // 创建服务客户端
    ros::ServiceClient set_model_state_client = 
        nh.serviceClient<gazebo_msgs::SetModelState>("/gazebo/set_model_state");
    
    std::cout << "Car Position Setter C++ started for model: " << model_name << std::endl;
    
    try {
        while (ros::ok()) {
            double x, y;
            
            // 使用简单的cin输入，类似Python版本
            std::cout << "Enter the x position: ";
            if (!(std::cin >> x)) {
                std::cout << "Invalid input for x position" << std::endl;
                std::cin.clear();
                std::cin.ignore(10000, '\n');
                continue;
            }
            
            std::cout << "Enter the y position: ";
            if (!(std::cin >> y)) {
                std::cout << "Invalid input for y position" << std::endl;
                std::cin.clear();
                std::cin.ignore(10000, '\n');
                continue;
            }
            
            double z = 0.0;  // 固定地面高度
            
            // 创建ModelState对象
            gazebo_msgs::ModelState model_state;
            model_state.model_name = model_name;
            model_state.pose.position.x = x;
            model_state.pose.position.y = y;
            model_state.pose.position.z = z;
            model_state.pose.orientation.x = 0.0;
            model_state.pose.orientation.y = 0.0;
            model_state.pose.orientation.z = 0.0;
            model_state.pose.orientation.w = 1.0;
            
            // 设置更新频率为30Hz
            ros::Rate rate(30);
            
            // 连续发布30次该位置
            bool success = false;
            for (int i = 0; i < 30; ++i) {
                gazebo_msgs::SetModelState srv;
                srv.request.model_state = model_state;
                
                if (set_model_state_client.call(srv)) {
                    if (srv.response.success) {
                        success = true;
                        if (i == 0) {  // 只在第一次成功时打印
                            ROS_INFO("Car model %s moved to position (%.2f, %.2f) successfully.", 
                                    model_name.c_str(), x, y);
                        }
                    } else {
                        ROS_WARN("Failed to move car model %s: %s", 
                                model_name.c_str(), srv.response.status_message.c_str());
                        std::cout << "Car model does not exist. Exiting the program." << std::endl;
                        return 1;
                    }
                } else {
                    ROS_ERROR("Service call failed");
                    return 1;
                }
                rate.sleep();
            }
            
            if (success) {
                std::cout << "✅ Position set successfully!" << std::endl;
            }
        }
    }
    catch (const std::exception& e) {
        ROS_ERROR("Exception: %s", e.what());
        return 1;
    }
    
    return 0;
}