#!/usr/bin/env python3
"""
识别结果监控器
监听VSWARM15上run_autonomous_swarm.sh的输出，提取距离估算和灯语识别结果
通过MQTT发送给实验控制器
"""

import re
import time
import json
import threading
import subprocess
import paho.mqtt.client as mqtt
from datetime import datetime
import queue

class RecognitionResultMonitor:
    def __init__(self):
        # MQTT配置 (与实验控制器相同)
        self.mqtt_config = {
            'broker': '192.168.11.58',
            'port': 1883,
            'username': 'b7986fb66280bcee',
            'password': 'Mx9B2EVhH79BY783Zp6rPcrFHv70kQIqJ2i9B4EbdevZ3M',
            'keepalive': 60
        }
        
        # 结果队列
        self.result_queue = queue.Queue()

        # 当前实验状态
        self.current_experiment_state = None
        self.waiting_for_result = False

        # 实验数据记录
        self.experiment_data = []

        # 数据文件 - 保存在VSWARM15本地
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.data_file = f"light_recognition_experiment_{timestamp}.csv"
        self.init_data_file()
        
        # 正则表达式模式 (根据run_autonomous_swarm.sh的实际输出调整)
        self.patterns = {
            # 距离估算模式: "距离=3.45m"
            'distance': re.compile(r'距离=(\d+\.?\d*)m'),
            
            # 灯语识别模式: "检测到灯语: 红色快闪 → 指令6 (红快闪)"
            'light_pattern': re.compile(r'检测到灯语:.*→\s*指令(\d+)'),
            
            # 空间信息: "TrackID1: 距离=3.45m, 方位=45.1°"
            'spatial_info': re.compile(r'TrackID\d+:\s*距离=(\d+\.?\d*)m,\s*方位=(\d+\.?\d*)°'),
            
            # 处理时间: "处理时间: 234ms"
            'processing_time': re.compile(r'处理时间:\s*(\d+)ms'),
        }
        
        # 初始化MQTT
        self.setup_mqtt()
        
        print("🔍 识别结果监控器已启动")
        print("📡 等待实验控制器信号...")

    def init_data_file(self):
        """初始化CSV数据文件"""
        import csv
        headers = [
            'trial_id', 'timestamp', 'distance_range_min', 'distance_range_max',
            'true_distance', 'vswarm11_x', 'vswarm11_y', 'vswarm15_x', 'vswarm15_y',
            'true_light_mode', 'estimated_distance', 'recognized_light_mode',
            'recognition_success', 'distance_error', 'processing_time_ms'
        ]

        with open(self.data_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(headers)

        print(f"📊 数据文件已创建: {self.data_file}")
    
    def setup_mqtt(self):
        """设置MQTT连接"""
        self.mqtt_client = mqtt.Client("recognition_monitor")
        self.mqtt_client.username_pw_set(
            self.mqtt_config['username'], 
            self.mqtt_config['password']
        )
        
        # 设置回调函数
        self.mqtt_client.on_connect = self.on_mqtt_connect
        self.mqtt_client.on_message = self.on_mqtt_message
        
        try:
            self.mqtt_client.connect(
                self.mqtt_config['broker'], 
                self.mqtt_config['port'], 
                self.mqtt_config['keepalive']
            )
            self.mqtt_client.loop_start()
            print("✅ MQTT连接成功")
        except Exception as e:
            print(f"❌ MQTT连接失败: {e}")
    
    def on_mqtt_connect(self, client, userdata, flags, rc):
        """MQTT连接回调"""
        if rc == 0:
            # 订阅实验状态话题
            client.subscribe("/experiment/state")
            client.subscribe("/experiment/light_command")
            print("📡 已订阅实验控制话题")
        else:
            print(f"❌ MQTT连接失败，返回码: {rc}")
    
    def on_mqtt_message(self, client, userdata, msg):
        """MQTT消息回调"""
        try:
            topic = msg.topic
            payload = json.loads(msg.payload.decode())
            
            if topic == "/experiment/state":
                # 接收到新的实验状态
                self.current_experiment_state = payload
                self.waiting_for_result = True
                print(f"🎯 接收到实验状态: 试验{payload.get('trial_id')}, "
                      f"距离{payload.get('true_distance', 0):.2f}m, "
                      f"灯语模式{payload.get('true_light_mode')}")
                
            elif topic == "/experiment/light_command":
                # 接收到灯语指令
                print(f"💡 接收到灯语指令: 模式{payload.get('light_mode')}")
                
        except Exception as e:
            print(f"❌ 处理MQTT消息失败: {e}")
    
    def parse_output_line(self, line):
        """解析run_autonomous_swarm.sh输出的单行"""
        results = {}
        
        # 提取距离信息
        distance_match = self.patterns['distance'].search(line)
        if distance_match:
            results['estimated_distance'] = float(distance_match.group(1))
        
        # 提取灯语识别信息
        light_match = self.patterns['light_pattern'].search(line)
        if light_match:
            results['recognized_light_mode'] = int(light_match.group(1))
        
        # 提取空间信息
        spatial_match = self.patterns['spatial_info'].search(line)
        if spatial_match:
            results['estimated_distance'] = float(spatial_match.group(1))
            results['azimuth'] = float(spatial_match.group(2))
        
        # 提取处理时间
        time_match = self.patterns['processing_time'].search(line)
        if time_match:
            results['processing_time_ms'] = int(time_match.group(1))
        
        return results if results else None
    
    def monitor_autonomous_swarm_output(self):
        """监控run_autonomous_swarm.sh的输出"""
        print("🚀 开始监控run_autonomous_swarm.sh输出...")
        
        try:
            # 启动run_autonomous_swarm.sh进程
            cmd = "cd /home/<USER>/BeeSwarm/Code/ros && ./run_autonomous_swarm.sh"
            process = subprocess.Popen(
                cmd, 
                shell=True, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            current_result = {}
            last_update_time = time.time()
            
            # 实时读取输出
            for line in iter(process.stdout.readline, ''):
                if line:
                    line = line.strip()
                    print(f"📝 输出: {line}")
                    
                    # 解析这一行
                    parsed = self.parse_output_line(line)
                    if parsed:
                        current_result.update(parsed)
                        last_update_time = time.time()
                        print(f"🔍 解析结果: {parsed}")
                    
                    # 如果正在等待结果且有足够信息，发送结果
                    if (self.waiting_for_result and 
                        self.current_experiment_state and
                        ('estimated_distance' in current_result or 'recognized_light_mode' in current_result)):
                        
                        # 等待一段时间收集更多信息
                        if time.time() - last_update_time > 2.0:
                            self.save_and_send_result(current_result)
                            current_result = {}
                            self.waiting_for_result = False
            
        except Exception as e:
            print(f"❌ 监控过程出错: {e}")
        finally:
            if 'process' in locals():
                process.terminate()
    
    def save_and_send_result(self, result):
        """保存识别结果到本地CSV并发送简单确认信号"""
        if not self.current_experiment_state:
            print("❌ 没有当前实验状态，无法保存结果")
            return

        # 构建完整的试验数据
        trial_data = {
            'trial_id': self.current_experiment_state.get('trial_id'),
            'timestamp': datetime.now().isoformat(),
            'distance_range_min': self.current_experiment_state.get('distance_range', [0, 0])[0],
            'distance_range_max': self.current_experiment_state.get('distance_range', [0, 0])[1],
            'true_distance': self.current_experiment_state.get('true_distance', -1),
            'vswarm11_x': self.current_experiment_state.get('vswarm11_position', [0, 0])[0],
            'vswarm11_y': self.current_experiment_state.get('vswarm11_position', [0, 0])[1],
            'vswarm15_x': self.current_experiment_state.get('vswarm15_position', [0, 0])[0],
            'vswarm15_y': self.current_experiment_state.get('vswarm15_position', [0, 0])[1],
            'true_light_mode': self.current_experiment_state.get('true_light_mode', -1),
            'estimated_distance': result.get('estimated_distance', -1),
            'recognized_light_mode': result.get('recognized_light_mode', -1),
            'recognition_success': False,
            'distance_error': -1,
            'processing_time_ms': result.get('processing_time_ms', -1)
        }

        # 计算识别成功率
        true_light_mode = self.current_experiment_state.get('true_light_mode')
        if true_light_mode is not None:
            trial_data['recognition_success'] = (
                result.get('recognized_light_mode', -1) == true_light_mode
            )

        # 计算距离误差
        true_distance = self.current_experiment_state.get('true_distance')
        if true_distance is not None and result.get('estimated_distance', -1) > 0:
            trial_data['distance_error'] = abs(
                result.get('estimated_distance') - true_distance
            )

        # 保存到本地CSV
        self.save_trial_data(trial_data)

        # 发送简单的完成确认信号给控制器
        try:
            completion_signal = {
                'type': 'trial_completed',
                'trial_id': trial_data['trial_id'],
                'timestamp': time.time(),
                'success': True
            }

            self.mqtt_client.publish(
                '/experiment/trial_completed',
                json.dumps(completion_signal)
            )

            print(f"📤 试验{trial_data['trial_id']}完成，数据已保存")
            print(f"   估算距离: {trial_data['estimated_distance']:.2f}m")
            print(f"   识别模式: {trial_data['recognized_light_mode']}")
            print(f"   识别成功: {trial_data['recognition_success']}")

        except Exception as e:
            print(f"❌ 发送完成信号失败: {e}")

    def save_trial_data(self, trial_data):
        """保存试验数据到CSV文件"""
        import csv
        try:
            with open(self.data_file, 'a', newline='') as f:
                writer = csv.writer(f)
                writer.writerow([
                    trial_data['trial_id'], trial_data['timestamp'],
                    trial_data['distance_range_min'], trial_data['distance_range_max'],
                    trial_data['true_distance'], trial_data['vswarm11_x'], trial_data['vswarm11_y'],
                    trial_data['vswarm15_x'], trial_data['vswarm15_y'], trial_data['true_light_mode'],
                    trial_data['estimated_distance'], trial_data['recognized_light_mode'],
                    trial_data['recognition_success'], trial_data['distance_error'],
                    trial_data['processing_time_ms']
                ])

            self.experiment_data.append(trial_data)
            print(f"💾 数据已保存到: {self.data_file}")

        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def run(self):
        """运行监控器"""
        try:
            # 在单独线程中监控输出
            monitor_thread = threading.Thread(
                target=self.monitor_autonomous_swarm_output,
                daemon=True
            )
            monitor_thread.start()
            
            print("🔄 监控器运行中... (按Ctrl+C停止)")
            
            # 主线程保持运行
            while True:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n⏹️ 用户停止监控器")
        except Exception as e:
            print(f"❌ 监控器运行出错: {e}")


def main():
    """主函数"""
    try:
        monitor = RecognitionResultMonitor()
        monitor.run()
        
    except Exception as e:
        print(f"程序错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
