#!/usr/bin/env python3
"""
生成灯语识别实验CSV模板
预填充真实数据，留空识别结果供手动填入
"""

import csv
import random
import math
from datetime import datetime, timedelta

class ExperimentTemplateGenerator:
    def __init__(self):
        # 实验配置
        self.experiment_config = {
            'distance_ranges': [
                (1.0, 2.0), (2.0, 3.0), (3.0, 4.0), (4.0, 5.0), (5.0, 6.0),
                (6.0, 7.0), (7.0, 8.0), (8.0, 9.0), (9.0, 10.0)
            ],
            'light_modes': list(range(1, 11)),  # 1-10 闪烁模式
            'trials_per_combination': 20,  # 每组20次
        }
        
        # Lab_106环境约束
        self.environment_bounds = {
            'x_min': -3.5, 'x_max': 3.5,
            'y_min': -4.5, 'y_max': 4.5,
            'safe_margin': 0.5
        }
        
        # 实验ID
        self.experiment_id = "light_recognition_exp_001"
        
    def generate_smart_positions(self, target_distance):
        """生成智能位置分布"""
        max_attempts = 100
        
        for attempt in range(max_attempts):
            # 随机选择VSWARM15位置 (观测车)
            vswarm15_x = random.uniform(
                self.environment_bounds['x_min'] + self.environment_bounds['safe_margin'],
                self.environment_bounds['x_max'] - self.environment_bounds['safe_margin']
            )
            vswarm15_y = random.uniform(
                self.environment_bounds['y_min'] + self.environment_bounds['safe_margin'],
                self.environment_bounds['y_max'] - self.environment_bounds['safe_margin']
            )
            
            # 随机选择方向角度
            angle = random.uniform(0, 2 * math.pi)
            
            # 计算VSWARM11位置 (发布车)
            vswarm11_x = vswarm15_x + target_distance * math.cos(angle)
            vswarm11_y = vswarm15_y + target_distance * math.sin(angle)
            
            # 检查VSWARM11是否在边界内
            if (self.environment_bounds['x_min'] + self.environment_bounds['safe_margin'] <= vswarm11_x <= 
                self.environment_bounds['x_max'] - self.environment_bounds['safe_margin'] and
                self.environment_bounds['y_min'] + self.environment_bounds['safe_margin'] <= vswarm11_y <= 
                self.environment_bounds['y_max'] - self.environment_bounds['safe_margin']):
                
                # 验证实际距离
                actual_distance = math.sqrt((vswarm11_x - vswarm15_x)**2 + (vswarm11_y - vswarm15_y)**2)
                
                return {
                    'vswarm11': (vswarm11_x, vswarm11_y),
                    'vswarm15': (vswarm15_x, vswarm15_y),
                    'actual_distance': actual_distance
                }
        
        # 如果找不到合适位置，使用简单的线性排列
        vswarm15_x, vswarm15_y = 0.0, 0.0
        vswarm11_x, vswarm11_y = target_distance, 0.0
        
        return {
            'vswarm11': (vswarm11_x, vswarm11_y),
            'vswarm15': (vswarm15_x, vswarm15_y),
            'actual_distance': target_distance
        }
    
    def generate_template(self, output_file=None):
        """生成实验模板CSV"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"experiment_template_{timestamp}.csv"
        
        # CSV表头
        headers = [
            'experiment_id', 'trial_id', 'timestamp',
            'distance_range_min', 'distance_range_max', 'true_distance',
            'true_light_mode', 'vswarm11_x', 'vswarm11_y', 'vswarm15_x', 'vswarm15_y',
            'estimated_distance', 'recognized_light_mode', 'confidence', 'processing_time_ms',
            'recognition_success', 'distance_error', 'notes'
        ]
        
        trial_number = 1
        base_time = datetime.now()
        
        print(f"🚀 开始生成实验模板...")
        print(f"📊 配置:")
        print(f"   距离区间: {len(self.experiment_config['distance_ranges'])} 个")
        print(f"   灯语模式: {len(self.experiment_config['light_modes'])} 种")
        print(f"   每组重复: {self.experiment_config['trials_per_combination']} 次")
        
        total_trials = (len(self.experiment_config['distance_ranges']) * 
                       len(self.experiment_config['light_modes']) * 
                       self.experiment_config['trials_per_combination'])
        print(f"   总试验数: {total_trials} 次")
        
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(headers)
            
            # 遍历所有距离区间
            for dist_range in self.experiment_config['distance_ranges']:
                print(f"\n📏 生成距离区间: {dist_range[0]}-{dist_range[1]}m")
                
                # 遍历所有灯语模式
                for light_mode in self.experiment_config['light_modes']:
                    print(f"   💡 灯语模式: {light_mode}")
                    
                    # 每个组合重复指定次数
                    for trial_in_group in range(self.experiment_config['trials_per_combination']):
                        # 生成随机距离
                        true_distance = random.uniform(dist_range[0], dist_range[1])
                        
                        # 生成位置
                        positions = self.generate_smart_positions(true_distance)
                        
                        # 生成时间戳 (每次试验间隔5秒)
                        trial_timestamp = base_time + timedelta(seconds=trial_number * 5)
                        
                        # 写入数据行
                        row = [
                            self.experiment_id,
                            f"trial_{trial_number:04d}",
                            trial_timestamp.isoformat(),
                            dist_range[0],
                            dist_range[1],
                            round(true_distance, 3),
                            light_mode,
                            round(positions['vswarm11'][0], 3),
                            round(positions['vswarm11'][1], 3),
                            round(positions['vswarm15'][0], 3),
                            round(positions['vswarm15'][1], 3),
                            '',  # estimated_distance - 待填入
                            '',  # recognized_light_mode - 待填入
                            '',  # confidence - 待填入
                            '',  # processing_time_ms - 待填入
                            '',  # recognition_success - 待填入
                            '',  # distance_error - 待填入
                            ''   # notes - 待填入
                        ]
                        
                        writer.writerow(row)
                        trial_number += 1
        
        print(f"\n✅ 模板生成完成!")
        print(f"📁 文件: {output_file}")
        print(f"📊 总计: {trial_number - 1} 条记录")
        print(f"\n📝 使用说明:")
        print(f"   1. 打开CSV文件")
        print(f"   2. 填入识别结果列:")
        print(f"      - estimated_distance: 估算距离")
        print(f"      - recognized_light_mode: 识别的灯语模式")
        print(f"      - confidence: 置信度 (0-1)")
        print(f"      - processing_time_ms: 处理时间(毫秒)")
        print(f"   3. 保存后可用analyze_experiment_results.py分析")
        
        return output_file
    
    def generate_quick_template(self, output_file=None):
        """生成快速测试模板 (少量数据用于验证)"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"quick_test_template_{timestamp}.csv"
        
        # 临时修改配置为快速测试
        original_config = self.experiment_config.copy()
        self.experiment_config = {
            'distance_ranges': [(2.0, 3.0), (5.0, 6.0)],  # 只测试2个距离区间
            'light_modes': [1, 6],  # 只测试红色慢闪和红色快闪
            'trials_per_combination': 5,   # 每组只测试5次
        }
        
        result = self.generate_template(output_file)
        
        # 恢复原配置
        self.experiment_config = original_config
        
        return result

    def generate_ultra_quick_template(self, output_file=None):
        """生成超快速测试模板 (极少量数据用于快速验证)"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"ultra_quick_template_{timestamp}.csv"

        # 临时修改配置为超快速测试
        original_config = self.experiment_config.copy()
        self.experiment_config = {
            'distance_ranges': [(2.0, 3.0), (5.0, 6.0)],  # 只测试2个距离区间
            'light_modes': [1, 6],  # 只测试红色慢闪和红色快闪
            'trials_per_combination': 2,   # 每组只测试2次
        }

        result = self.generate_template(output_file)

        # 恢复原配置
        self.experiment_config = original_config

        return result


def main():
    """主函数"""
    print("🎯 灯语识别实验模板生成器")
    print("="*50)
    
    generator = ExperimentTemplateGenerator()
    
    print("\n请选择模板类型:")
    print("1. 完整实验模板 (1,800条记录)")
    print("2. 快速测试模板 (20条记录)")
    print("3. 超快速模板 (10条记录)")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-3): ").strip()

            if choice == "1":
                output_file = generator.generate_template()
                break
            elif choice == "2":
                output_file = generator.generate_quick_template()
                break
            elif choice == "3":
                output_file = generator.generate_ultra_quick_template()
                break
            else:
                print("❌ 无效选择，请输入1-3")
        except KeyboardInterrupt:
            print("\n👋 用户取消")
            return
    
    print(f"\n🎉 模板已生成: {output_file}")
    print("现在你可以手动填入识别结果，然后使用分析脚本进行数据分析！")


if __name__ == '__main__':
    main()
