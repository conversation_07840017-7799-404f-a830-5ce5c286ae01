# 🎯 BeeSwarm灯语识别实验自动化系统

## 📍 项目位置
```
/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/light_recognition_experiment/
```

## 🎯 实验目标
评估在1-10m距离梯度下，5种颜色×2种时序模式的灯语组合的视觉可识别性，通过区间随机采样获取稳定统计量。

## 📁 文件结构
```
light_recognition_experiment/
├── README.md                                    # 本文件
├── start_experiment.sh                          # 一键启动脚本
├── light_recognition_experiment_controller.py   # 实验控制器 (VSWARM11)
├── recognition_result_monitor.py                # 识别监控器 (VSWARM15)
├── analyze_experiment_results.py                # 数据分析脚本
└── experiment_usage_guide.md                    # 详细使用指南
```

## 🚀 快速开始

### 1. 在VSWARM15上启动监控器
```bash
cd /home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/light_recognition_experiment
./start_experiment.sh
# 选择选项2: VSWARM15 - 识别监控器
```

### 2. 在VSWARM11上启动控制器
```bash
cd /home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/light_recognition_experiment
./start_experiment.sh
# 选择选项1: VSWARM11 - 实验控制器
```

### 3. 实验完成后分析数据 (在VSWARM15上)
```bash
python3 analyze_experiment_results.py
```

## 📊 实验模式选择

### 🎯 完整实验模式
- **距离区间**: 9个 (1-2m, 2-3m, ..., 9-10m)
- **灯语模式**: 10种 (5色×2时序，排除常亮模式0)
- **每组重复**: 10次随机距离
- **总试验数**: 900次 (9×10×10)
- **预计时间**: ~1.4小时

### ⚡ 快速测试模式
- **距离区间**: 2个 (2-3m, 5-6m)
- **灯语模式**: 2种 (红色慢闪、红色快闪)
- **每组重复**: 5次随机距离
- **总试验数**: 20次
- **预计时间**: ~2分钟

### 🚀 超快速模式
- **距离区间**: 2个 (2-3m, 5-6m)
- **灯语模式**: 2种 (红色慢闪、红色快闪)
- **每组重复**: 2次随机距离
- **总试验数**: 8次
- **预计时间**: ~1分钟

### 🔧 自定义模式
- 可自定义重复次数和等待时间
- 适合特定需求的测试

## 💾 数据输出
- **CSV文件**: 保存在VSWARM15的当前目录
- **文件名**: `light_recognition_experiment_YYYYMMDD_HHMMSS.csv`
- **分析报告**: 包含准确率图表、混淆矩阵、距离误差分析

## 🔧 核心特性
- ✅ 区间随机距离采样
- ✅ 基于lab_106.world的智能位置分布
- ✅ MQTT实时通信同步
- ✅ 自动化数据记录和分析
- ✅ 时间戳精确匹配

## 📖 详细文档
查看 `experiment_usage_guide.md` 获取完整的使用说明、配置选项和故障排除指南。

## 🏗️ 系统架构
```
VSWARM11 (发布机器)          VSWARM15 (观测机器)
┌─────────────────────┐      ┌─────────────────────┐
│ 实验控制器           │      │ 识别结果监控器       │
│ - 位置控制          │      │ - 监听输出          │
│ - 灯语发布          │ MQTT │ - 解析结果          │
│ - 状态广播          │◄────►│ - 数据记录 📊       │
└─────────────────────┘      └─────────────────────┘
```

## ⚠️ 注意事项
1. 确保两台机器的仿真环境都在运行
2. 确认MQTT代理服务器(192.168.11.58)可访问
3. 先启动VSWARM15监控器，再启动VSWARM11控制器
4. 实验数据保存在VSWARM15上，请确保有足够磁盘空间

---
*BeeSwarm Team - 2025*
